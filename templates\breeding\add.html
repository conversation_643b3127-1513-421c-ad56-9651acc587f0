{% extends "base.html" %}

{% block title %}تسجيل تكاثر - {{ cattle.tag_number }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-heart"></i> تسجيل تكاثر للبقرة: {{ cattle.tag_number }}</h1>
            <a href="{{ url_for('cattle_detail', id=cattle.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-heart"></i> بيانات التكاثر
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="breeding_date" class="form-label">تاريخ التلقيح *</label>
                            <input type="date" class="form-control" id="breeding_date" name="breeding_date" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="expected_calving" class="form-label">تاريخ الولادة المتوقع</label>
                            <input type="date" class="form-control" id="expected_calving" readonly>
                            <div class="form-text">يتم حسابه تلقائياً (280 يوم من التلقيح)</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="bull_info" class="form-label">معلومات الثور</label>
                        <textarea class="form-control" id="bull_info" name="bull_info" rows="3" 
                                  placeholder="اسم الثور، السلالة، رقم التسجيل، إلخ..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> سيتم تغيير حالة البقرة إلى "حامل" تلقائياً بعد التسجيل.
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('cattle_detail', id=cattle.id) }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> تسجيل التكاثر
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('breeding_date').value = moment().format('YYYY-MM-DD');
    
    // حساب تاريخ الولادة المتوقع
    function calculateExpectedCalving() {
        const breedingDate = document.getElementById('breeding_date').value;
        if (breedingDate) {
            const expectedDate = moment(breedingDate).add(280, 'days').format('YYYY-MM-DD');
            document.getElementById('expected_calving').value = expectedDate;
        }
    }
    
    // ربط الحساب بتغيير التاريخ
    document.getElementById('breeding_date').addEventListener('change', calculateExpectedCalving);
    
    // حساب أولي
    calculateExpectedCalving();
</script>
{% endblock %}
