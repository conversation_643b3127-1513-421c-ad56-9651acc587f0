from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, date, timedelta
import os

def calculate_age(birth_date):
    """حساب العمر بالسنوات"""
    if not birth_date:
        return None
    today = date.today()
    age = today.year - birth_date.year
    if today < birth_date.replace(year=today.year):
        age -= 1
    return age

def days_until_date(target_date):
    """حساب عدد الأيام حتى تاريخ معين"""
    if not target_date:
        return None
    today = date.today()
    return (target_date - today).days

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///cattle_management.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)

# إضافة الدوال للقوالب
@app.template_filter('days_until')
def days_until_filter(target_date):
    """فلتر لحساب الأيام حتى تاريخ معين"""
    return days_until_date(target_date)

# إضافة دالة مساعدة للقوالب
@app.template_filter('calculate_age')
def calculate_age_filter(birth_date):
    """حساب العمر للاستخدام في القوالب"""
    return calculate_age(birth_date)

# نماذج قاعدة البيانات
class Cattle(db.Model):
    __tablename__ = 'cattle'
    
    id = db.Column(db.Integer, primary_key=True)
    tag_number = db.Column(db.String(20), unique=True, nullable=False)
    name = db.Column(db.String(100))
    breed = db.Column(db.String(50))
    gender = db.Column(db.String(10))
    birth_date = db.Column(db.Date)
    weight = db.Column(db.Float)
    color = db.Column(db.String(50))
    status = db.Column(db.String(20), default='نشط')
    purchase_price = db.Column(db.Float)
    purchase_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    health_records = db.relationship('HealthRecord', backref='cattle', lazy=True)
    milk_records = db.relationship('MilkProduction', backref='cattle', lazy=True)
    breeding_records = db.relationship('BreedingRecord', backref='cattle', lazy=True)

class HealthRecord(db.Model):
    __tablename__ = 'health_records'
    
    id = db.Column(db.Integer, primary_key=True)
    cattle_id = db.Column(db.Integer, db.ForeignKey('cattle.id'), nullable=False)
    record_type = db.Column(db.String(50))  # تطعيم، علاج، فحص
    description = db.Column(db.Text)
    date = db.Column(db.Date)
    veterinarian = db.Column(db.String(100))
    cost = db.Column(db.Float)
    next_due_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class MilkProduction(db.Model):
    __tablename__ = 'milk_production'
    
    id = db.Column(db.Integer, primary_key=True)
    cattle_id = db.Column(db.Integer, db.ForeignKey('cattle.id'), nullable=False)
    date = db.Column(db.Date)
    morning_amount = db.Column(db.Float, default=0)
    evening_amount = db.Column(db.Float, default=0)
    total_amount = db.Column(db.Float)
    quality_grade = db.Column(db.String(20))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class BreedingRecord(db.Model):
    __tablename__ = 'breeding_records'
    
    id = db.Column(db.Integer, primary_key=True)
    cattle_id = db.Column(db.Integer, db.ForeignKey('cattle.id'), nullable=False)
    breeding_date = db.Column(db.Date)
    bull_info = db.Column(db.String(200))
    expected_calving_date = db.Column(db.Date)
    actual_calving_date = db.Column(db.Date)
    pregnancy_status = db.Column(db.String(20))
    calf_info = db.Column(db.Text)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class FinancialRecord(db.Model):
    __tablename__ = 'financial_records'
    
    id = db.Column(db.Integer, primary_key=True)
    transaction_type = db.Column(db.String(20))  # دخل، مصروف
    category = db.Column(db.String(50))
    description = db.Column(db.Text)
    amount = db.Column(db.Float)
    date = db.Column(db.Date)
    cattle_id = db.Column(db.Integer, db.ForeignKey('cattle.id'), nullable=True)

    # العلاقة مع الماشية
    cattle = db.relationship('Cattle', backref='financial_records')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# نموذج الإعدادات
class Settings(db.Model):
    __tablename__ = 'settings'

    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# نموذج المواد العلفية
class FeedIngredient(db.Model):
    __tablename__ = 'feed_ingredients'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # اسم المادة العلفية
    category = db.Column(db.String(50))  # نوع المادة (حبوب، بروتين، فيتامينات، إلخ)
    protein_percentage = db.Column(db.Float)  # نسبة البروتين
    energy_value = db.Column(db.Float)  # القيمة الطاقية (ميجا جول/كغ)
    fiber_percentage = db.Column(db.Float)  # نسبة الألياف
    fat_percentage = db.Column(db.Float)  # نسبة الدهون
    moisture_percentage = db.Column(db.Float)  # نسبة الرطوبة
    price_per_kg = db.Column(db.Float)  # السعر لكل كيلو
    supplier = db.Column(db.String(100))  # المورد
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# نموذج الخلطات العلفية
class FeedMix(db.Model):
    __tablename__ = 'feed_mixes'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # اسم الخلطة
    description = db.Column(db.Text)  # وصف الخلطة
    target_group = db.Column(db.String(50))  # الفئة المستهدفة (أبقار حلوب، عجول، إلخ)
    total_protein = db.Column(db.Float)  # إجمالي البروتين
    total_energy = db.Column(db.Float)  # إجمالي الطاقة
    total_cost_per_kg = db.Column(db.Float)  # التكلفة الإجمالية لكل كيلو
    daily_amount_per_head = db.Column(db.Float)  # الكمية اليومية لكل رأس
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقة مع مكونات الخلطة
    ingredients = db.relationship('FeedMixIngredient', backref='feed_mix', lazy=True, cascade='all, delete-orphan')

# نموذج مكونات الخلطة
class FeedMixIngredient(db.Model):
    __tablename__ = 'feed_mix_ingredients'

    id = db.Column(db.Integer, primary_key=True)
    feed_mix_id = db.Column(db.Integer, db.ForeignKey('feed_mixes.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('feed_ingredients.id'), nullable=False)
    percentage = db.Column(db.Float, nullable=False)  # النسبة في الخلطة
    weight_kg = db.Column(db.Float)  # الوزن بالكيلو

    # العلاقات
    ingredient = db.relationship('FeedIngredient', backref='mix_usages')

# دالة مساعدة للإعدادات
def get_setting(key, default_value=None):
    """الحصول على قيمة إعداد"""
    setting = Settings.query.filter_by(key=key).first()
    return setting.value if setting else default_value

def set_setting(key, value):
    """تعيين قيمة إعداد"""
    setting = Settings.query.filter_by(key=key).first()
    if setting:
        setting.value = value
        setting.updated_at = datetime.utcnow()
    else:
        setting = Settings(key=key, value=value)
        db.session.add(setting)
    db.session.commit()

# الصفحة الرئيسية
@app.route('/')
def index():
    total_cattle = Cattle.query.count()
    active_cattle = Cattle.query.filter_by(status='نشط').count()
    
    # إحصائيات الحليب لهذا الشهر
    current_month = datetime.now().month
    current_year = datetime.now().year
    monthly_milk = db.session.query(db.func.sum(MilkProduction.total_amount)).filter(
        db.extract('month', MilkProduction.date) == current_month,
        db.extract('year', MilkProduction.date) == current_year
    ).scalar() or 0
    
    # الإيرادات الشهرية
    monthly_income = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
        FinancialRecord.transaction_type == 'دخل',
        db.extract('month', FinancialRecord.date) == current_month,
        db.extract('year', FinancialRecord.date) == current_year
    ).scalar() or 0
    
    # الحصول على إعدادات المزرعة
    farm_name = get_setting('farm_name', 'مزرعة الأمل')
    milk_price = float(get_setting('milk_price', '0.40'))

    return render_template('index.html',
                         total_cattle=total_cattle,
                         active_cattle=active_cattle,
                         monthly_milk=monthly_milk,
                         monthly_income=monthly_income,
                         farm_name=farm_name,
                         milk_price=milk_price)

# إدارة الأبقار
@app.route('/cattle')
def cattle_list():
    cattle = Cattle.query.all()
    print(f"Debug: عدد الأبقار: {len(cattle)}")
    for cow in cattle:
        print(f"Debug: بقرة {cow.tag_number}: الجنس = '{cow.gender}'")
    return render_template('cattle/list.html', cattle=cattle)

@app.route('/cattle/add', methods=['GET', 'POST'])
def add_cattle():
    if request.method == 'POST':
        print(f"Debug: إضافة بقرة جديدة")
        print(f"Debug: الجنس المُرسل: '{request.form.get('gender', 'غير محدد')}'")
        print(f"Debug: جميع البيانات: {dict(request.form)}")

        cattle = Cattle(
            tag_number=request.form['tag_number'],
            name=request.form['name'],
            breed=request.form['breed'],
            gender=request.form['gender'],
            birth_date=datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date() if request.form['birth_date'] else None,
            weight=float(request.form['weight']) if request.form['weight'] else None,
            color=request.form['color'],
            purchase_price=float(request.form['purchase_price']) if request.form['purchase_price'] else None,
            purchase_date=datetime.strptime(request.form['purchase_date'], '%Y-%m-%d').date() if request.form['purchase_date'] else None,
            notes=request.form['notes']
        )
        
        try:
            db.session.add(cattle)
            db.session.commit()
            flash('تم إضافة البقرة بنجاح!', 'success')
            return redirect(url_for('cattle_list'))
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في إضافة البقرة: {str(e)}")
            flash(f'حدث خطأ في إضافة البقرة: {str(e)}', 'error')
    
    return render_template('cattle/add.html')

# اختيار بقرة عامة لإضافة سجل صحي
@app.route('/health/select', methods=['GET'])
def select_cattle_for_health():
    cattle = Cattle.query.all()
    return render_template('health/select_cattle.html', cattle=cattle)

# اختيار بقرة عامة لتسجيل الحليب
@app.route('/milk/select', methods=['GET'])
def select_cattle_for_milk():
    cattle = Cattle.query.all()
    return render_template('milk/select_cattle.html', cattle=cattle)

# اختيار بقرة عامة لتسجيل التكاثر
@app.route('/breeding/select', methods=['GET'])
def select_cattle_for_breeding():
    cattle = Cattle.query.all()
    return render_template('breeding/select_cattle.html', cattle=cattle)

# مسارات توافقية لإعادة التوجيه لمن يفتح /health/add أو /milk/add بدون تحديد بقرة
@app.route('/health/add')
def health_add_redirect():
    return redirect(url_for('select_cattle_for_health'))

@app.route('/milk/add')
def milk_add_redirect():
    return redirect(url_for('select_cattle_for_milk'))

@app.route('/breeding/add')
def breeding_add_redirect():
    return redirect(url_for('select_cattle_for_breeding'))

@app.route('/cattle/edit/<int:id>', methods=['GET', 'POST'])
def edit_cattle(id):
    cattle = Cattle.query.get_or_404(id)

    if request.method == 'POST':
        cattle.tag_number = request.form['tag_number']
        cattle.name = request.form['name']
        cattle.breed = request.form['breed']
        cattle.gender = request.form['gender']
        cattle.birth_date = datetime.strptime(request.form['birth_date'], '%Y-%m-%d').date() if request.form['birth_date'] else None
        cattle.weight = float(request.form['weight']) if request.form['weight'] else None
        cattle.color = request.form['color']
        cattle.purchase_price = float(request.form['purchase_price']) if request.form['purchase_price'] else None
        cattle.purchase_date = datetime.strptime(request.form['purchase_date'], '%Y-%m-%d').date() if request.form['purchase_date'] else None
        cattle.notes = request.form['notes']
        cattle.status = request.form['status']

        try:
            db.session.commit()
            flash('تم تحديث بيانات البقرة بنجاح!', 'success')
            return redirect(url_for('cattle_detail', id=id))
        except Exception:
            db.session.rollback()
            flash('حدث خطأ في تحديث البيانات!', 'error')

    return render_template('cattle/edit.html', cattle=cattle)

@app.route('/cattle/delete/<int:id>', methods=['POST'])
def delete_cattle(id):
    cattle = Cattle.query.get_or_404(id)

    try:
        # حذف السجلات المرتبطة أولاً
        HealthRecord.query.filter_by(cattle_id=id).delete()
        MilkProduction.query.filter_by(cattle_id=id).delete()
        BreedingRecord.query.filter_by(cattle_id=id).delete()
        FinancialRecord.query.filter_by(cattle_id=id).delete()

        # حذف البقرة
        db.session.delete(cattle)
        db.session.commit()
        flash('تم حذف البقرة وجميع سجلاتها بنجاح!', 'success')
    except Exception:
        db.session.rollback()
        flash('حدث خطأ في حذف البقرة!', 'error')

    return redirect(url_for('cattle_list'))

@app.route('/cattle/<int:id>')
def cattle_detail(id):
    cattle = Cattle.query.get_or_404(id)
    health_records = HealthRecord.query.filter_by(cattle_id=id).order_by(HealthRecord.date.desc()).all()
    milk_records = MilkProduction.query.filter_by(cattle_id=id).order_by(MilkProduction.date.desc()).limit(10).all()
    breeding_records = BreedingRecord.query.filter_by(cattle_id=id).order_by(BreedingRecord.breeding_date.desc()).all()
    
    # الحصول على سعر الحليب الحالي
    milk_price = float(get_setting('milk_price', '0.40'))

    return render_template('cattle/detail.html',
                         cattle=cattle,
                         health_records=health_records,
                         milk_records=milk_records,
                         breeding_records=breeding_records,
                         milk_price=milk_price)

# إدارة السجلات الصحية
@app.route('/health')
def health_records():
    records = HealthRecord.query.join(Cattle).order_by(HealthRecord.date.desc()).all()
    return render_template('health/list.html', records=records)

@app.route('/health/add/<int:cattle_id>', methods=['GET', 'POST'])
def add_health_record(cattle_id):
    cattle = Cattle.query.get_or_404(cattle_id)

    if request.method == 'POST':
        record = HealthRecord(
            cattle_id=cattle_id,
            record_type=request.form['record_type'],
            description=request.form['description'],
            date=datetime.strptime(request.form['date'], '%Y-%m-%d').date(),
            veterinarian=request.form['veterinarian'],
            cost=float(request.form['cost']) if request.form['cost'] else 0,
            next_due_date=datetime.strptime(request.form['next_due_date'], '%Y-%m-%d').date() if request.form['next_due_date'] else None,
            notes=request.form['notes']
        )

        try:
            db.session.add(record)
            db.session.commit()
            flash('تم إضافة السجل الصحي بنجاح!', 'success')
            return redirect(url_for('cattle_detail', id=cattle_id))
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في إضافة السجل الصحي: {str(e)}")
            flash(f'حدث خطأ في إضافة السجل: {str(e)}', 'error')

    return render_template('health/add.html', cattle=cattle)

# عرض تفاصيل السجل الصحي
@app.route('/health/view/<int:record_id>')
def view_health_record(record_id):
    record = HealthRecord.query.get_or_404(record_id)
    return render_template('health/view.html', record=record)

# تعديل السجل الصحي
@app.route('/health/edit/<int:record_id>', methods=['GET', 'POST'])
def edit_health_record(record_id):
    record = HealthRecord.query.get_or_404(record_id)

    if request.method == 'POST':
        record.record_type = request.form['record_type']
        record.description = request.form['description']
        record.date = datetime.strptime(request.form['date'], '%Y-%m-%d').date()
        record.veterinarian = request.form['veterinarian']
        record.cost = float(request.form['cost']) if request.form['cost'] else 0
        record.next_due_date = datetime.strptime(request.form['next_due_date'], '%Y-%m-%d').date() if request.form['next_due_date'] else None
        record.notes = request.form['notes']

        try:
            db.session.commit()
            flash('تم تحديث السجل الصحي بنجاح!', 'success')
            return redirect(url_for('cattle_detail', id=record.cattle_id))
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في تحديث السجل الصحي: {str(e)}")
            flash(f'حدث خطأ في تحديث السجل: {str(e)}', 'error')

    return render_template('health/edit.html', record=record)

# حذف السجل الصحي
@app.route('/health/delete/<int:record_id>', methods=['POST'])
def delete_health_record(record_id):
    record = HealthRecord.query.get_or_404(record_id)
    cattle_id = record.cattle_id

    try:
        db.session.delete(record)
        db.session.commit()
        flash('تم حذف السجل الصحي بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"خطأ في حذف السجل الصحي: {str(e)}")
        flash(f'حدث خطأ في حذف السجل: {str(e)}', 'error')

    return redirect(url_for('cattle_detail', id=cattle_id))

@app.route('/financial')
def financial_reports():
    # إحصائيات شهرية
    current_month = datetime.now().month
    current_year = datetime.now().year

    monthly_income = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
        FinancialRecord.transaction_type == 'دخل',
        db.extract('month', FinancialRecord.date) == current_month,
        db.extract('year', FinancialRecord.date) == current_year
    ).scalar() or 0

    monthly_expenses = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
        FinancialRecord.transaction_type == 'مصروف',
        db.extract('month', FinancialRecord.date) == current_month,
        db.extract('year', FinancialRecord.date) == current_year
    ).scalar() or 0

    net_profit = monthly_income - monthly_expenses

    recent_transactions = FinancialRecord.query.order_by(FinancialRecord.date.desc()).limit(10).all()

    # الحصول على سعر الحليب الحالي
    milk_price = float(get_setting('milk_price', '0.80'))
    milk_price_updated = get_setting('milk_price_updated', 'غير محدد')

    return render_template('financial/reports.html',
                         monthly_income=monthly_income,
                         monthly_expenses=monthly_expenses,
                         net_profit=net_profit,
                         recent_transactions=recent_transactions,
                         milk_price=milk_price,
                         milk_price_updated=milk_price_updated)



# إدارة إنتاج الحليب
@app.route('/milk')
def milk_production():
    records = MilkProduction.query.join(Cattle).order_by(MilkProduction.date.desc()).all()

    # الحصول على سعر الحليب الحالي
    milk_price = float(get_setting('milk_price', '0.80'))

    return render_template('milk/list.html', records=records, milk_price=milk_price)

@app.route('/milk/add/<int:cattle_id>', methods=['GET', 'POST'])
def add_milk_record(cattle_id):
    cattle = Cattle.query.get_or_404(cattle_id)
    current_milk_price = float(get_setting('milk_price', '0.80'))

    if request.method == 'POST':
        morning = float(request.form['morning_amount']) if request.form['morning_amount'] else 0
        evening = float(request.form['evening_amount']) if request.form['evening_amount'] else 0

        record = MilkProduction(
            cattle_id=cattle_id,
            date=datetime.strptime(request.form['date'], '%Y-%m-%d').date(),
            morning_amount=morning,
            evening_amount=evening,
            total_amount=morning + evening,
            quality_grade=request.form['quality_grade'],
            notes=request.form['notes']
        )

        try:
            db.session.add(record)
            db.session.commit()
            flash('تم تسجيل إنتاج الحليب بنجاح!', 'success')
            return redirect(url_for('cattle_detail', id=cattle_id))
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في تسجيل إنتاج الحليب: {str(e)}")
            flash(f'حدث خطأ في تسجيل الإنتاج: {str(e)}', 'error')

    return render_template('milk/add.html', cattle=cattle, milk_price=current_milk_price)

# عرض تفاصيل سجل الحليب
@app.route('/milk/view/<int:record_id>')
def view_milk_record(record_id):
    record = MilkProduction.query.get_or_404(record_id)
    return render_template('milk/view.html', record=record)

# تعديل سجل الحليب
@app.route('/milk/edit/<int:record_id>', methods=['GET', 'POST'])
def edit_milk_record(record_id):
    record = MilkProduction.query.get_or_404(record_id)

    if request.method == 'POST':
        morning = float(request.form['morning_amount']) if request.form['morning_amount'] else 0
        evening = float(request.form['evening_amount']) if request.form['evening_amount'] else 0

        record.date = datetime.strptime(request.form['date'], '%Y-%m-%d').date()
        record.morning_amount = morning
        record.evening_amount = evening
        record.total_amount = morning + evening
        record.quality_grade = request.form['quality_grade']
        record.notes = request.form['notes']

        try:
            db.session.commit()
            flash('تم تحديث سجل الحليب بنجاح!', 'success')
            return redirect(url_for('cattle_detail', id=record.cattle_id))
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في تحديث سجل الحليب: {str(e)}")
            flash(f'حدث خطأ في تحديث السجل: {str(e)}', 'error')

    # الحصول على سعر الحليب الحالي
    current_milk_price = float(get_setting('milk_price', '0.40'))

    return render_template('milk/edit.html', record=record, milk_price=current_milk_price)

# حذف سجل الحليب
@app.route('/milk/delete/<int:record_id>', methods=['POST'])
def delete_milk_record(record_id):
    record = MilkProduction.query.get_or_404(record_id)
    cattle_id = record.cattle_id

    try:
        db.session.delete(record)
        db.session.commit()
        flash('تم حذف سجل الحليب بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"خطأ في حذف سجل الحليب: {str(e)}")
        flash(f'حدث خطأ في حذف السجل: {str(e)}', 'error')

    return redirect(url_for('cattle_detail', id=cattle_id))

# إدارة التكاثر
@app.route('/breeding')
def breeding_records():
    records = BreedingRecord.query.join(Cattle).order_by(BreedingRecord.breeding_date.desc()).all()
    return render_template('breeding/list.html', records=records)

@app.route('/breeding/add/<int:cattle_id>', methods=['GET', 'POST'])
def add_breeding_record(cattle_id):
    cattle = Cattle.query.get_or_404(cattle_id)

    if request.method == 'POST':
        # حساب تاريخ الولادة المتوقع (280 يوم)
        breeding_date = datetime.strptime(request.form['breeding_date'], '%Y-%m-%d').date()
        expected_calving = breeding_date + timedelta(days=280)

        record = BreedingRecord(
            cattle_id=cattle_id,
            breeding_date=breeding_date,
            bull_info=request.form['bull_info'],
            expected_calving_date=expected_calving,
            pregnancy_status='في انتظار التأكيد',
            notes=request.form['notes']
        )

        try:
            db.session.add(record)
            # تحديث حالة البقرة إلى حامل
            if hasattr(cattle, 'status'):
                cattle.status = 'حامل'
            db.session.commit()
            flash('تم تسجيل التكاثر بنجاح!', 'success')
            return redirect(url_for('cattle_detail', id=cattle_id))
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في تسجيل التكاثر: {str(e)}")
            flash(f'حدث خطأ في تسجيل التكاثر: {str(e)}', 'error')

    return render_template('breeding/add.html', cattle=cattle)

# إدارة المعاملات المالية
@app.route('/financial/add', methods=['GET', 'POST'])
def add_financial_transaction():
    if request.method == 'POST':
        record = FinancialRecord(
            transaction_type=request.form['transaction_type'],
            category=request.form['category'],
            description=request.form['description'],
            amount=float(request.form['amount']),
            date=datetime.strptime(request.form['date'], '%Y-%m-%d').date(),
            cattle_id=int(request.form['cattle_id']) if request.form['cattle_id'] else None,
            notes=request.form['notes']
        )

        try:
            db.session.add(record)
            db.session.commit()
            flash('تم إضافة المعاملة المالية بنجاح!', 'success')
            return redirect(url_for('financial_reports'))
        except Exception:
            db.session.rollback()
            flash('حدث خطأ في إضافة المعاملة!', 'error')

    cattle_list = Cattle.query.all()
    return render_template('financial/add_transaction.html', cattle_list=cattle_list)

# تعديل المعاملة المالية
@app.route('/financial/edit/<int:transaction_id>', methods=['GET', 'POST'])
def edit_financial_transaction(transaction_id):
    transaction = FinancialRecord.query.get_or_404(transaction_id)

    if request.method == 'POST':
        try:
            transaction.transaction_type = request.form['transaction_type']
            transaction.category = request.form['category']
            transaction.description = request.form['description']
            transaction.amount = float(request.form['amount'])
            transaction.date = datetime.strptime(request.form['date'], '%Y-%m-%d').date()
            transaction.cattle_id = int(request.form['cattle_id']) if request.form['cattle_id'] else None
            transaction.notes = request.form['notes']

            db.session.commit()
            flash('تم تحديث المعاملة المالية بنجاح!', 'success')
            return redirect(url_for('financial_reports'))
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في تحديث المعاملة: {str(e)}")
            flash(f'حدث خطأ في تحديث المعاملة: {str(e)}', 'error')

    cattle_list = Cattle.query.all()
    return render_template('financial/edit.html', transaction=transaction, cattle_list=cattle_list)

# حذف المعاملة المالية
@app.route('/financial/delete/<int:transaction_id>', methods=['POST'])
def delete_financial_transaction(transaction_id):
    transaction = FinancialRecord.query.get_or_404(transaction_id)

    try:
        db.session.delete(transaction)
        db.session.commit()
        flash('تم حذف المعاملة المالية بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"خطأ في حذف المعاملة: {str(e)}")
        flash(f'حدث خطأ في حذف المعاملة: {str(e)}', 'error')

    return redirect(url_for('financial_reports'))

# عرض تفاصيل المعاملة المالية
@app.route('/financial/view/<int:transaction_id>')
def view_financial_transaction(transaction_id):
    transaction = FinancialRecord.query.get_or_404(transaction_id)
    return render_template('financial/view.html', transaction=transaction)

# تقارير متقدمة
@app.route('/reports/monthly')
def monthly_report():
    """تقرير شهري مفصل"""
    current_month = datetime.now().month
    current_year = datetime.now().year

    # إحصائيات الحليب
    monthly_milk = db.session.query(db.func.sum(MilkProduction.total_amount)).filter(
        db.extract('month', MilkProduction.date) == current_month,
        db.extract('year', MilkProduction.date) == current_year
    ).scalar() or 0

    # إحصائيات مالية
    monthly_income = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
        FinancialRecord.transaction_type == 'دخل',
        db.extract('month', FinancialRecord.date) == current_month,
        db.extract('year', FinancialRecord.date) == current_year
    ).scalar() or 0

    monthly_expenses = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
        FinancialRecord.transaction_type == 'مصروف',
        db.extract('month', FinancialRecord.date) == current_month,
        db.extract('year', FinancialRecord.date) == current_year
    ).scalar() or 0

    # إحصائيات الصحة
    monthly_health_costs = db.session.query(db.func.sum(HealthRecord.cost)).filter(
        db.extract('month', HealthRecord.date) == current_month,
        db.extract('year', HealthRecord.date) == current_year
    ).scalar() or 0

    # الحصول على سعر الحليب الحالي
    milk_price = float(get_setting('milk_price', '0.80'))

    return render_template('reports/monthly.html',
                         monthly_milk=monthly_milk,
                         monthly_income=monthly_income,
                         monthly_expenses=monthly_expenses,
                         monthly_health_costs=monthly_health_costs,
                         net_profit=monthly_income - monthly_expenses,
                         milk_price=milk_price)

# حفظ التقرير الشهري
@app.route('/reports/monthly/save', methods=['POST'])
def save_monthly_report():
    try:
        current_month = datetime.now().month
        current_year = datetime.now().year

        # جمع بيانات التقرير
        monthly_milk = db.session.query(db.func.sum(MilkProduction.total_amount)).filter(
            db.extract('month', MilkProduction.date) == current_month,
            db.extract('year', MilkProduction.date) == current_year
        ).scalar() or 0

        monthly_income = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
            FinancialRecord.transaction_type == 'دخل',
            db.extract('month', FinancialRecord.date) == current_month,
            db.extract('year', FinancialRecord.date) == current_year
        ).scalar() or 0

        monthly_expenses = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
            FinancialRecord.transaction_type == 'مصروف',
            db.extract('month', FinancialRecord.date) == current_month,
            db.extract('year', FinancialRecord.date) == current_year
        ).scalar() or 0

        # حفظ التقرير كمعاملة مالية خاصة
        report_description = f"تقرير شهري - {current_year}/{current_month:02d}"
        net_profit = monthly_income - monthly_expenses

        # يمكن حفظ التقرير في جدول منفصل أو كملف
        flash(f'تم حفظ التقرير الشهري بنجاح! صافي الربح: {net_profit:.2f} د.أ', 'success')

    except Exception as e:
        print(f"خطأ في حفظ التقرير: {str(e)}")
        flash(f'حدث خطأ في حفظ التقرير: {str(e)}', 'error')

    return redirect(url_for('monthly_report'))

# تصدير التقرير الشهري
@app.route('/reports/monthly/export')
def export_monthly_report():
    try:
        current_month = datetime.now().month
        current_year = datetime.now().year

        # جمع بيانات التقرير
        monthly_milk = db.session.query(db.func.sum(MilkProduction.total_amount)).filter(
            db.extract('month', MilkProduction.date) == current_month,
            db.extract('year', MilkProduction.date) == current_year
        ).scalar() or 0

        monthly_income = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
            FinancialRecord.transaction_type == 'دخل',
            db.extract('month', FinancialRecord.date) == current_month,
            db.extract('year', FinancialRecord.date) == current_year
        ).scalar() or 0

        monthly_expenses = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
            FinancialRecord.transaction_type == 'مصروف',
            db.extract('month', FinancialRecord.date) == current_month,
            db.extract('year', FinancialRecord.date) == current_year
        ).scalar() or 0

        # إنشاء محتوى CSV
        import io
        output = io.StringIO()
        output.write("التقرير الشهري المفصل\n")
        output.write(f"الشهر: {current_year}/{current_month:02d}\n")
        output.write(f"تاريخ التصدير: {datetime.now().strftime('%Y/%m/%d %H:%M')}\n\n")
        output.write("الملخص المالي:\n")
        output.write(f"إجمالي الإيرادات,{monthly_income:.2f} د.أ\n")
        output.write(f"إجمالي المصروفات,{monthly_expenses:.2f} د.أ\n")
        output.write(f"صافي الربح,{monthly_income - monthly_expenses:.2f} د.أ\n")
        output.write(f"إجمالي إنتاج الحليب,{monthly_milk:.1f} لتر\n")

        # إرجاع الملف
        from flask import Response
        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename=monthly_report_{current_year}_{current_month:02d}.csv'}
        )

    except Exception as e:
        print(f"خطأ في تصدير التقرير: {str(e)}")
        flash(f'حدث خطأ في تصدير التقرير: {str(e)}', 'error')
        return redirect(url_for('monthly_report'))

# إدارة التقارير المحفوظة
@app.route('/reports/saved')
def saved_reports():
    # يمكن إنشاء جدول منفصل للتقارير المحفوظة
    # حالياً سنعرض التقارير المتاحة
    reports = [
        {
            'id': 1,
            'name': 'التقرير الشهري',
            'type': 'monthly',
            'description': 'تقرير شهري مفصل للإيرادات والمصروفات',
            'last_generated': datetime.now(),
            'url': url_for('monthly_report')
        },
        {
            'id': 2,
            'name': 'لوحة التحكم المتقدمة',
            'type': 'dashboard',
            'description': 'مؤشرات الأداء والإحصائيات المتقدمة',
            'last_generated': datetime.now(),
            'url': url_for('advanced_dashboard')
        },
        {
            'id': 3,
            'name': 'تقرير الأداء الشامل',
            'type': 'performance',
            'description': 'تحليل شامل لأداء المزرعة والأبقار مع مؤشرات الكفاءة والربحية',
            'last_generated': datetime.now(),
            'url': url_for('performance_report')
        }
    ]

    return render_template('reports/saved.html', reports=reports)

# حذف تقرير محفوظ
@app.route('/reports/delete/<int:report_id>', methods=['POST'])
def delete_saved_report(report_id):
    try:
        # حذف التقرير من قاعدة البيانات (عند إنشاء جدول التقارير)
        flash('تم حذف التقرير بنجاح!', 'success')
    except Exception as e:
        print(f"خطأ في حذف التقرير: {str(e)}")
        flash(f'حدث خطأ في حذف التقرير: {str(e)}', 'error')

    return redirect(url_for('saved_reports'))

# تصدير تقرير مخصص
@app.route('/reports/custom/export', methods=['POST'])
def export_custom_report():
    try:
        start_date = datetime.strptime(request.form['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(request.form['end_date'], '%Y-%m-%d').date()
        report_type = request.form['report_type']

        # جمع البيانات حسب النوع
        if report_type == 'financial':
            transactions = FinancialRecord.query.filter(
                FinancialRecord.date >= start_date,
                FinancialRecord.date <= end_date
            ).all()

            # إنشاء CSV
            import io
            output = io.StringIO()
            output.write("التقرير المالي المخصص\n")
            output.write(f"من: {start_date} إلى: {end_date}\n\n")
            output.write("التاريخ,النوع,الفئة,الوصف,المبلغ,البقرة,ملاحظات\n")

            for transaction in transactions:
                cattle_tag = transaction.cattle.tag_number if transaction.cattle_id else 'عام'
                output.write(f"{transaction.date},{transaction.transaction_type},{transaction.category},{transaction.description},{transaction.amount},{cattle_tag},{transaction.notes or ''}\n")

            from flask import Response
            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename=financial_report_{start_date}_{end_date}.csv'}
            )

        elif report_type == 'milk':
            milk_records = MilkProduction.query.filter(
                MilkProduction.date >= start_date,
                MilkProduction.date <= end_date
            ).all()

            # إنشاء CSV للحليب
            import io
            output = io.StringIO()
            output.write("تقرير إنتاج الحليب\n")
            output.write(f"من: {start_date} إلى: {end_date}\n\n")
            output.write("التاريخ,البقرة,الصباح,المساء,الإجمالي,ملاحظات\n")

            for record in milk_records:
                output.write(f"{record.date},{record.cattle.tag_number},{record.morning_amount},{record.evening_amount},{record.total_amount},{record.notes or ''}\n")

            from flask import Response
            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename=milk_report_{start_date}_{end_date}.csv'}
            )

        flash('نوع التقرير غير مدعوم!', 'error')
        return redirect(url_for('saved_reports'))

    except Exception as e:
        print(f"خطأ في تصدير التقرير المخصص: {str(e)}")
        flash(f'حدث خطأ في تصدير التقرير: {str(e)}', 'error')
        return redirect(url_for('saved_reports'))

# لوحة التحكم المتقدمة
@app.route('/dashboard')
def advanced_dashboard():
    # إحصائيات شاملة
    total_cattle = Cattle.query.count()
    active_cattle = Cattle.query.filter_by(status='نشط').count()
    pregnant_cattle = Cattle.query.filter_by(status='حامل').count()

    # إنتاج الحليب اليومي
    today = date.today()
    daily_milk = db.session.query(db.func.sum(MilkProduction.total_amount)).filter_by(date=today).scalar() or 0

    # التنبيهات الصحية
    health_alerts = 0  # سيتم تطويرها لاحقاً

    # الربح الشهري
    current_month = datetime.now().month
    current_year = datetime.now().year

    monthly_income = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
        FinancialRecord.transaction_type == 'دخل',
        db.extract('month', FinancialRecord.date) == current_month,
        db.extract('year', FinancialRecord.date) == current_year
    ).scalar() or 0

    monthly_expenses = db.session.query(db.func.sum(FinancialRecord.amount)).filter(
        FinancialRecord.transaction_type == 'مصروف',
        db.extract('month', FinancialRecord.date) == current_month,
        db.extract('year', FinancialRecord.date) == current_year
    ).scalar() or 0

    monthly_profit = monthly_income - monthly_expenses

    # الحصول على سعر الحليب الحالي
    milk_price = float(get_setting('milk_price', '0.40'))
    milk_price_updated = get_setting('milk_price_updated', 'غير محدد')

    return render_template('reports/dashboard.html',
                         total_cattle=total_cattle,
                         active_cattle=active_cattle,
                         pregnant_cattle=pregnant_cattle,
                         daily_milk=daily_milk,
                         health_alerts=health_alerts,
                         monthly_profit=monthly_profit,
                         milk_price=milk_price,
                         milk_price_updated=milk_price_updated)

# تقرير الأداء الشامل
@app.route('/reports/performance')
def performance_report(template='reports/performance_simple.html'):
    """تقرير أداء شامل للمزرعة"""
    from datetime import datetime, date, timedelta

    # الحصول على التواريخ من المعاملات أو استخدام آخر 30 يوم
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')

    if start_date_str and end_date_str:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    else:
        end_date = date.today()
        start_date = end_date - timedelta(days=30)

    days_count = (end_date - start_date).days + 1

    # إحصائيات عامة
    total_cattle = Cattle.query.count()
    active_cattle = Cattle.query.filter_by(status='نشط').count()

    # إنتاج الحليب في الفترة المحددة
    milk_records = MilkProduction.query.filter(
        MilkProduction.date >= start_date,
        MilkProduction.date <= end_date
    ).all()

    total_milk_production = sum(record.total_amount for record in milk_records)
    avg_daily_production = total_milk_production / days_count if days_count > 0 else 0

    # الحصول على سعر الحليب
    milk_price = float(get_setting('milk_price', '0.40'))
    total_milk_value = total_milk_production * milk_price

    # الإيرادات والمصروفات
    income_records = FinancialRecord.query.filter(
        FinancialRecord.transaction_type == 'دخل',
        FinancialRecord.date >= start_date,
        FinancialRecord.date <= end_date
    ).all()

    expense_records = FinancialRecord.query.filter(
        FinancialRecord.transaction_type == 'مصروف',
        FinancialRecord.date >= start_date,
        FinancialRecord.date <= end_date
    ).all()

    total_income = sum(record.amount for record in income_records)
    total_expenses = sum(record.amount for record in expense_records)
    net_profit = total_income - total_expenses

    # مؤشرات الكفاءة
    cost_per_liter = total_expenses / total_milk_production if total_milk_production > 0 else 0
    profit_per_liter = net_profit / total_milk_production if total_milk_production > 0 else 0
    profit_margin = (net_profit / total_income * 100) if total_income > 0 else 0
    production_per_cattle = total_milk_production / active_cattle if active_cattle > 0 else 0

    # العائد على الاستثمار (تقدير بسيط)
    total_investment = sum(cattle.purchase_price for cattle in Cattle.query.all() if cattle.purchase_price)
    roi = (net_profit / total_investment * 100) if total_investment > 0 else 0

    # أداء الأبقار الفردي
    cattle_performances = []
    for cattle in Cattle.query.filter_by(gender='أنثى').all():
        cattle_milk_records = [r for r in milk_records if r.cattle_id == cattle.id]
        cattle_total = sum(r.total_amount for r in cattle_milk_records)
        cattle_avg = cattle_total / days_count if days_count > 0 else 0
        cattle_value = cattle_total * milk_price

        if cattle_total > 0:  # فقط الأبقار المنتجة
            cattle_performances.append({
                'tag_number': cattle.tag_number,
                'name': cattle.name,
                'total_production': cattle_total,
                'avg_daily': cattle_avg,
                'estimated_value': cattle_value
            })

    # ترتيب الأبقار حسب الأداء
    cattle_performances.sort(key=lambda x: x['avg_daily'], reverse=True)
    top_performers = cattle_performances[:5]
    low_performers = [c for c in cattle_performances if c['avg_daily'] < 5]

    # إحصائيات الجودة
    quality_stats = {
        'excellent': len([r for r in milk_records if r.quality_grade == 'ممتاز']),
        'very_good': len([r for r in milk_records if r.quality_grade == 'جيد جداً']),
        'good': len([r for r in milk_records if r.quality_grade == 'جيد']),
        'acceptable': len([r for r in milk_records if r.quality_grade == 'مقبول'])
    }

    # تحليل التكاليف
    feed_costs = sum(r.amount for r in expense_records if 'علف' in r.category.lower())
    medical_costs = sum(r.amount for r in expense_records if 'طبي' in r.category.lower())
    maintenance_costs = sum(r.amount for r in expense_records if 'صيانة' in r.category.lower())
    other_costs = total_expenses - feed_costs - medical_costs - maintenance_costs

    expense_breakdown = {
        'feed_amount': feed_costs,
        'medical_amount': medical_costs,
        'maintenance_amount': maintenance_costs,
        'other_amount': other_costs,
        'feed_percentage': (feed_costs / total_expenses * 100) if total_expenses > 0 else 0,
        'medical_percentage': (medical_costs / total_expenses * 100) if total_expenses > 0 else 0,
        'maintenance_percentage': (maintenance_costs / total_expenses * 100) if total_expenses > 0 else 0,
        'other_percentage': (other_costs / total_expenses * 100) if total_expenses > 0 else 0
    }

    # إحصائيات الصحة
    health_stats = {
        'healthy': active_cattle,
        'needs_attention': 0,  # سيتم تطويرها
        'sick': 0  # سيتم تطويرها
    }

    # بيانات الاتجاهات (آخر 7 أيام)
    trends_data = []
    for i in range(7):
        trend_date = end_date - timedelta(days=i)
        daily_milk = sum(r.total_amount for r in milk_records if r.date == trend_date)
        daily_income = sum(r.amount for r in income_records if r.date == trend_date)

        trends_data.append({
            'date': trend_date.strftime('%m/%d'),
            'milk_production': daily_milk,
            'income': daily_income
        })

    trends_data.reverse()  # ترتيب تصاعدي

    return render_template(template,
                         start_date=start_date.strftime('%Y-%m-%d'),
                         end_date=end_date.strftime('%Y-%m-%d'),
                         days_count=days_count,
                         total_cattle=total_cattle,
                         total_milk_production=total_milk_production,
                         avg_daily_production=avg_daily_production,
                         total_income=total_income,
                         total_expenses=total_expenses,
                         net_profit=net_profit,
                         cost_per_liter=cost_per_liter,
                         profit_per_liter=profit_per_liter,
                         profit_margin=profit_margin,
                         production_per_cattle=production_per_cattle,
                         roi=roi,
                         cattle_performances=cattle_performances,
                         top_performers=top_performers,
                         low_performers=low_performers,
                         quality_stats=quality_stats,
                         health_stats=health_stats,
                         medical_costs=medical_costs,
                         expense_breakdown=expense_breakdown,
                         total_milk_value=total_milk_value,
                         trends_data=trends_data,
                         report_date=datetime.now().strftime('%Y/%m/%d %H:%M'))

@app.route('/reports/performance/simple')
def performance_report_simple():
    """نسخة مبسطة من تقرير الأداء"""
    return performance_report(template='reports/performance_simple.html')

@app.route('/reports/performance/export')
def export_performance_report():
    """تصدير تقرير الأداء بصيغ مختلفة"""
    from datetime import datetime, date, timedelta
    import json
    from flask import make_response

    # الحصول على التواريخ
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')
    export_format = request.args.get('format', 'json')

    if start_date_str and end_date_str:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    else:
        end_date = date.today()
        start_date = end_date - timedelta(days=30)

    # جمع البيانات (نفس منطق تقرير الأداء)
    total_cattle = Cattle.query.count()

    milk_records = MilkProduction.query.filter(
        MilkProduction.date >= start_date,
        MilkProduction.date <= end_date
    ).all()

    total_milk_production = sum(record.total_amount for record in milk_records)
    days_count = (end_date - start_date).days + 1
    avg_daily_production = total_milk_production / days_count if days_count > 0 else 0

    # الإيرادات والمصروفات
    income_records = FinancialRecord.query.filter(
        FinancialRecord.transaction_type == 'دخل',
        FinancialRecord.date >= start_date,
        FinancialRecord.date <= end_date
    ).all()

    expense_records = FinancialRecord.query.filter(
        FinancialRecord.transaction_type == 'مصروف',
        FinancialRecord.date >= start_date,
        FinancialRecord.date <= end_date
    ).all()

    total_income = sum(record.amount for record in income_records)
    total_expenses = sum(record.amount for record in expense_records)
    net_profit = total_income - total_expenses
    profit_margin = (net_profit / total_income * 100) if total_income > 0 else 0

    # أداء الأبقار
    cattle_performances = []
    milk_price = float(get_setting('milk_price', '0.40'))

    for cattle in Cattle.query.filter_by(gender='أنثى').all():
        cattle_milk_records = [r for r in milk_records if r.cattle_id == cattle.id]
        cattle_total = sum(r.total_amount for r in cattle_milk_records)
        cattle_avg = cattle_total / days_count if days_count > 0 else 0
        cattle_value = cattle_total * milk_price

        if cattle_total > 0:
            cattle_performances.append({
                'tag_number': cattle.tag_number,
                'name': cattle.name,
                'total_production': cattle_total,
                'avg_daily': cattle_avg,
                'estimated_value': cattle_value
            })

    # إعداد البيانات للتصدير
    export_data = {
        'title': 'تقرير الأداء الشامل',
        'period': {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'days_count': days_count
        },
        'summary': {
            'total_cattle': total_cattle,
            'total_milk_production': round(total_milk_production, 1),
            'avg_daily_production': round(avg_daily_production, 1),
            'total_income': round(total_income, 2),
            'total_expenses': round(total_expenses, 2),
            'net_profit': round(net_profit, 2),
            'profit_margin': round(profit_margin, 1)
        },
        'cattle_performance': cattle_performances,
        'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }

    if export_format == 'json':
        response = make_response(json.dumps(export_data, ensure_ascii=False, indent=2))
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=performance_report_{start_date}_{end_date}.json'
        return response

    elif export_format == 'csv':
        import io
        output = io.StringIO()

        # كتابة البيانات الأساسية
        output.write('المؤشر,القيمة\n')
        output.write(f'إجمالي الأبقار,{total_cattle}\n')
        output.write(f'إجمالي إنتاج الحليب (لتر),{total_milk_production:.1f}\n')
        output.write(f'متوسط الإنتاج اليومي (لتر),{avg_daily_production:.1f}\n')
        output.write(f'إجمالي الإيرادات (د.أ),{total_income:.2f}\n')
        output.write(f'إجمالي المصروفات (د.أ),{total_expenses:.2f}\n')
        output.write(f'صافي الربح (د.أ),{net_profit:.2f}\n')
        output.write(f'هامش الربح (%),{profit_margin:.1f}\n\n')

        # كتابة أداء الأبقار
        output.write('البقرة,إجمالي الإنتاج,متوسط يومي,القيمة المقدرة\n')
        for cattle in cattle_performances:
            name = f"{cattle['tag_number']}"
            if cattle['name']:
                name += f" - {cattle['name']}"
            output.write(f"{name},{cattle['total_production']:.1f},{cattle['avg_daily']:.1f},{cattle['estimated_value']:.2f}\n")

        response = make_response(output.getvalue())
        response.headers['Content-Type'] = 'text/csv; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=performance_report_{start_date}_{end_date}.csv'
        return response

    elif export_format == 'pdf':
        # تصدير PDF باستخدام HTML
        html_content = render_template('reports/performance_simple.html',
                                     start_date=start_date.strftime('%Y-%m-%d'),
                                     end_date=end_date.strftime('%Y-%m-%d'),
                                     days_count=days_count,
                                     total_cattle=total_cattle,
                                     total_milk_production=total_milk_production,
                                     avg_daily_production=avg_daily_production,
                                     total_income=total_income,
                                     total_expenses=total_expenses,
                                     net_profit=net_profit,
                                     profit_margin=profit_margin,
                                     cattle_performances=cattle_performances,
                                     report_date=datetime.now().strftime('%Y/%m/%d %H:%M'))

        response = make_response(html_content)
        response.headers['Content-Type'] = 'text/html; charset=utf-8'
        response.headers['Content-Disposition'] = f'attachment; filename=performance_report_{start_date}_{end_date}.html'
        return response

    else:
        flash('تم تصدير تقرير الأداء بنجاح!', 'success')
        return redirect(url_for('performance_report_simple'))

@app.route('/reports/performance/save')
def save_performance_report():
    """حفظ تقرير الأداء في قاعدة البيانات"""
    from datetime import datetime, date, timedelta

    # الحصول على التواريخ
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')

    if start_date_str and end_date_str:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    else:
        end_date = date.today()
        start_date = end_date - timedelta(days=30)

    # إنشاء تقرير محفوظ
    report_title = f"تقرير الأداء من {start_date} إلى {end_date}"
    report_content = f"""
    تقرير الأداء الشامل
    الفترة: {start_date} إلى {end_date}

    المؤشرات الرئيسية:
    - إجمالي الأبقار: {Cattle.query.count()}
    - إجمالي إنتاج الحليب: {sum(r.total_amount for r in MilkProduction.query.filter(MilkProduction.date >= start_date, MilkProduction.date <= end_date).all()):.1f} لتر

    تم إنشاء التقرير في: {datetime.now().strftime('%Y/%m/%d %H:%M')}
    """

    # حفظ في جدول التقارير المحفوظة (إذا كان موجوداً)
    try:
        saved_report = SavedReport(
            title=report_title,
            content=report_content,
            report_type='performance',
            created_at=datetime.now()
        )
        db.session.add(saved_report)
        db.session.commit()
        flash('تم حفظ تقرير الأداء بنجاح!', 'success')
    except Exception as e:
        flash('حدث خطأ في حفظ التقرير', 'error')

    return redirect(url_for('performance_report_simple'))

# API للبيانات
@app.route('/api/cattle/<int:id>/summary')
def cattle_summary_api(id):
    cattle = Cattle.query.get_or_404(id)

    # إحصائيات الحليب
    total_milk = db.session.query(db.func.sum(MilkProduction.total_amount)).filter_by(cattle_id=id).scalar() or 0
    avg_daily_milk = db.session.query(db.func.avg(MilkProduction.total_amount)).filter_by(cattle_id=id).scalar() or 0

    return jsonify({
        'tag_number': cattle.tag_number,
        'name': cattle.name,
        'total_milk_production': total_milk,
        'average_daily_milk': round(avg_daily_milk, 2),
        'health_records_count': len(cattle.health_records),
        'breeding_records_count': len(cattle.breeding_records)
    })

@app.route('/api/milk/weekly')
def weekly_milk_api():
    """API لبيانات إنتاج الحليب الأسبوعي"""
    # آخر 7 أيام
    end_date = date.today()
    start_date = end_date - timedelta(days=6)

    daily_production = []
    for i in range(7):
        day = start_date + timedelta(days=i)
        production = db.session.query(db.func.sum(MilkProduction.total_amount)).filter_by(date=day).scalar() or 0
        daily_production.append({
            'date': day.strftime('%Y-%m-%d'),
            'production': production
        })

    return jsonify(daily_production)

# الإعدادات
@app.route('/settings')
def settings():
    # الحصول على الإعدادات المحفوظة
    farm_name = get_setting('farm_name', 'مزرعة الأمل')
    owner_name = get_setting('owner_name', '')
    location = get_setting('location', '')
    milk_price = get_setting('milk_price', '0.40')
    vaccination_alerts = get_setting('vaccination_alerts', 'true') == 'true'
    health_alerts = get_setting('health_alerts', 'true') == 'true'
    breeding_alerts = get_setting('breeding_alerts', 'true') == 'true'
    alert_days = get_setting('alert_days', '7')

    # جمع إحصائيات النظام
    total_cattle = Cattle.query.count()
    total_milk_records = MilkProduction.query.count()
    total_financial_records = FinancialRecord.query.count()
    total_health_records = HealthRecord.query.count()

    return render_template('settings/index.html',
                         farm_name=farm_name,
                         owner_name=owner_name,
                         location=location,
                         milk_price=float(milk_price),
                         milk_price_updated=get_setting('milk_price_updated', 'غير محدد'),
                         vaccination_alerts=vaccination_alerts,
                         health_alerts=health_alerts,
                         breeding_alerts=breeding_alerts,
                         alert_days=alert_days,
                         total_cattle=total_cattle,
                         total_milk_records=total_milk_records,
                         total_financial_records=total_financial_records,
                         total_health_records=total_health_records)

# حفظ إعدادات المزرعة
@app.route('/settings/save', methods=['POST'])
def save_settings():
    try:
        # حفظ إعدادات المزرعة
        if 'farm_name' in request.form:
            set_setting('farm_name', request.form['farm_name'])
        if 'owner_name' in request.form:
            set_setting('owner_name', request.form['owner_name'])
        if 'location' in request.form:
            set_setting('location', request.form['location'])
        if 'milk_price' in request.form:
            set_setting('milk_price', request.form['milk_price'])

        # حفظ إعدادات التنبيهات
        if 'vaccination_alerts' in request.form:
            set_setting('vaccination_alerts', 'true')
        else:
            set_setting('vaccination_alerts', 'false')

        if 'health_alerts' in request.form:
            set_setting('health_alerts', 'true')
        else:
            set_setting('health_alerts', 'false')

        if 'breeding_alerts' in request.form:
            set_setting('breeding_alerts', 'true')
        else:
            set_setting('breeding_alerts', 'false')

        if 'alert_days' in request.form:
            set_setting('alert_days', request.form['alert_days'])

        flash('تم حفظ الإعدادات بنجاح!', 'success')
    except Exception as e:
        print(f"خطأ في حفظ الإعدادات: {str(e)}")
        flash(f'حدث خطأ في حفظ الإعدادات: {str(e)}', 'error')

    return redirect(url_for('settings'))

# تحديث سعر الحليب سريع
@app.route('/settings/milk-price/update', methods=['POST'])
def update_milk_price():
    try:
        new_price = float(request.form['milk_price'])
        if new_price < 0:
            flash('سعر الحليب يجب أن يكون أكبر من الصفر!', 'error')
        else:
            set_setting('milk_price', str(new_price))
            set_setting('milk_price_updated', datetime.now().strftime('%Y/%m/%d %H:%M'))
            flash(f'تم تحديث سعر الحليب إلى {new_price:.2f} د.أ/لتر بنجاح!', 'success')
    except ValueError:
        flash('يرجى إدخال سعر صحيح!', 'error')
    except Exception as e:
        print(f"خطأ في تحديث سعر الحليب: {str(e)}")
        flash(f'حدث خطأ في تحديث السعر: {str(e)}', 'error')

    # العودة للصفحة التي جاء منها المستخدم
    return redirect(request.referrer or url_for('settings'))

# API للحصول على سعر الحليب الحالي
@app.route('/api/milk-price')
def get_current_milk_price():
    try:
        current_price = float(get_setting('milk_price', '0.40'))
        return jsonify({
            'price': current_price,
            'currency': 'د.أ',
            'last_updated': get_setting('milk_price_updated', 'غير محدد')
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 400

# إضافة المواد العلفية الأساسية
@app.route('/feed/ingredients/add-basic', methods=['POST'])
def add_basic_ingredients():
    """إضافة المواد العلفية الأساسية"""
    try:
        # قائمة المواد العلفية الأساسية
        basic_ingredients = [
            {'name': 'شعير', 'category': 'حبوب', 'protein_percentage': 12.0, 'energy_value': 2800, 'price_per_kg': 0.45},
            {'name': 'ذرة صفراء', 'category': 'حبوب', 'protein_percentage': 9.0, 'energy_value': 3200, 'price_per_kg': 0.50},
            {'name': 'كسبة فول الصويا', 'category': 'بروتين', 'protein_percentage': 44.0, 'energy_value': 2400, 'price_per_kg': 0.80},
            {'name': 'برسيم مجفف', 'category': 'أعلاف خضراء', 'protein_percentage': 18.0, 'energy_value': 2200, 'price_per_kg': 0.35},
            {'name': 'نخالة قمح', 'category': 'مخلفات', 'protein_percentage': 16.0, 'energy_value': 2000, 'price_per_kg': 0.30},
            {'name': 'خليط فيتامينات ومعادن', 'category': 'إضافات', 'protein_percentage': 0.0, 'energy_value': 0, 'price_per_kg': 5.00},
            {'name': 'ملح طعام', 'category': 'إضافات', 'protein_percentage': 0.0, 'energy_value': 0, 'price_per_kg': 0.25}
        ]

        added_count = 0
        for ingredient_data in basic_ingredients:
            # التحقق من عدم وجود المادة مسبقاً
            existing = FeedIngredient.query.filter_by(name=ingredient_data['name']).first()
            if not existing:
                ingredient = FeedIngredient(
                    name=ingredient_data['name'],
                    category=ingredient_data['category'],
                    protein_percentage=ingredient_data['protein_percentage'],
                    energy_value=ingredient_data['energy_value'],
                    price_per_kg=ingredient_data['price_per_kg']
                )
                db.session.add(ingredient)
                added_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'تم إضافة {added_count} مادة علفية جديدة',
            'added_count': added_count
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500

# نسخة احتياطية من البيانات
@app.route('/settings/backup')
def backup_data():
    try:
        import shutil
        from datetime import datetime
        from flask import send_file

        # إنشاء اسم ملف النسخة الاحتياطية مع التاريخ
        backup_filename = f"cattle_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"

        # استخدام مجلد مؤقت للنسخ الاحتياطية
        import tempfile
        temp_dir = tempfile.gettempdir()
        backup_path = os.path.join(temp_dir, backup_filename)

        print(f"مسار النسخة الاحتياطية: {backup_path}")

        # نسخ قاعدة البيانات
        # البحث عن قاعدة البيانات في عدة مواقع محتملة
        possible_paths = [
            os.path.join('instance', 'cattle_management.db'),  # في بيئة التطوير
            'cattle_management.db',  # في نفس مجلد الملف التنفيذي
            os.path.join(os.path.dirname(__file__), 'cattle_management.db'),  # بجانب app.py
            os.path.join(os.getcwd(), 'cattle_management.db')  # في مجلد العمل الحالي
        ]

        db_path = None
        for path in possible_paths:
            if os.path.exists(path):
                db_path = path
                break

        if db_path:
            shutil.copy2(db_path, backup_path)
            print(f"تم العثور على قاعدة البيانات في: {db_path}")
            print(f"تم إنشاء النسخة الاحتياطية في: {backup_path}")

            # إرسال الملف للتحميل
            return send_file(backup_path, as_attachment=True, download_name=backup_filename)
        else:
            print(f"لم يتم العثور على قاعدة البيانات في المسارات: {possible_paths}")
            flash('لم يتم العثور على قاعدة البيانات!', 'error')

    except Exception as e:
        print(f"خطأ في النسخة الاحتياطية: {str(e)}")
        flash(f'حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}', 'error')

    return redirect(url_for('settings'))

# استعادة النسخة الاحتياطية
@app.route('/settings/restore', methods=['GET', 'POST'])
def restore_data():
    if request.method == 'POST':
        try:
            import shutil

            # التحقق من وجود ملف النسخة الاحتياطية
            if 'backup_file' not in request.files:
                flash('يرجى اختيار ملف النسخة الاحتياطية!', 'error')
                return redirect(url_for('settings'))

            file = request.files['backup_file']
            if file.filename == '':
                flash('لم يتم اختيار أي ملف!', 'error')
                return redirect(url_for('settings'))

            # التحقق من نوع الملف
            if not file.filename.endswith(('.db', '.sql', '.backup')):
                flash('نوع الملف غير مدعوم! يرجى اختيار ملف .db أو .sql أو .backup', 'error')
                return redirect(url_for('settings'))

            # حفظ الملف المرفوع مؤقتاً
            temp_path = os.path.join('temp_restore.db')
            file.save(temp_path)

            # إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
            # البحث عن قاعدة البيانات الحالية
            possible_paths = [
                os.path.join('instance', 'cattle_management.db'),
                'cattle_management.db',
                os.path.join(os.path.dirname(__file__), 'cattle_management.db'),
                os.path.join(os.getcwd(), 'cattle_management.db')
            ]

            current_db = None
            for path in possible_paths:
                if os.path.exists(path):
                    current_db = path
                    break

            backup_current = os.path.join('backups', f"before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db")
            os.makedirs('backups', exist_ok=True)

            if current_db and os.path.exists(current_db):
                shutil.copy2(current_db, backup_current)
                # استعادة النسخة الاحتياطية
                shutil.copy2(temp_path, current_db)
            else:
                # إنشاء قاعدة بيانات جديدة
                current_db = 'cattle_management.db'
                shutil.copy2(temp_path, current_db)

            # حذف الملف المؤقت
            os.remove(temp_path)

            flash('تم استعادة النسخة الاحتياطية بنجاح! يرجى إعادة تشغيل التطبيق.', 'success')

        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")
            flash(f'حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}', 'error')

        return redirect(url_for('settings'))

    return render_template('settings/restore.html')

# تصدير البيانات
@app.route('/settings/export')
def export_data():
    try:
        from flask import send_file
        import shutil

        # إنشاء نسخة للتصدير
        export_filename = f"cattle_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"

        # استخدام مجلد مؤقت للتصدير
        import tempfile
        temp_dir = tempfile.gettempdir()
        export_path = os.path.join(temp_dir, export_filename)

        print(f"مسار التصدير: {export_path}")

        # نسخ قاعدة البيانات للتصدير
        # البحث عن قاعدة البيانات في عدة مواقع محتملة
        possible_paths = [
            os.path.join('instance', 'cattle_management.db'),  # في بيئة التطوير
            'cattle_management.db',  # في نفس مجلد الملف التنفيذي
            os.path.join(os.path.dirname(__file__), 'cattle_management.db'),  # بجانب app.py
            os.path.join(os.getcwd(), 'cattle_management.db')  # في مجلد العمل الحالي
        ]

        db_path = None
        for path in possible_paths:
            if os.path.exists(path):
                db_path = path
                break

        if db_path:
            shutil.copy2(db_path, export_path)
            print(f"تم العثور على قاعدة البيانات في: {db_path}")
            print(f"تم إنشاء ملف التصدير في: {export_path}")

            # إرسال الملف للتحميل
            return send_file(export_path, as_attachment=True, download_name=export_filename)
        else:
            print(f"لم يتم العثور على قاعدة البيانات في المسارات: {possible_paths}")
            flash('لم يتم العثور على قاعدة البيانات للتصدير!', 'error')

    except Exception as e:
        print(f"خطأ في تصدير البيانات: {str(e)}")
        flash(f'حدث خطأ في تصدير البيانات: {str(e)}', 'error')

    return redirect(url_for('settings'))

# إدارة المواد العلفية
@app.route('/feed/ingredients')
def feed_ingredients():
    ingredients = FeedIngredient.query.all()
    return render_template('feed/ingredients.html', ingredients=ingredients)

@app.route('/feed/ingredients/add', methods=['GET', 'POST'])
def add_feed_ingredient():
    if request.method == 'POST':
        ingredient = FeedIngredient(
            name=request.form['name'],
            category=request.form['category'],
            protein_percentage=float(request.form['protein_percentage']) if request.form['protein_percentage'] else None,
            energy_value=float(request.form['energy_value']) if request.form['energy_value'] else None,
            fiber_percentage=float(request.form['fiber_percentage']) if request.form['fiber_percentage'] else None,
            fat_percentage=float(request.form['fat_percentage']) if request.form['fat_percentage'] else None,
            moisture_percentage=float(request.form['moisture_percentage']) if request.form['moisture_percentage'] else None,
            price_per_kg=float(request.form['price_per_kg']) if request.form['price_per_kg'] else None,
            supplier=request.form['supplier'],
            notes=request.form['notes']
        )

        try:
            db.session.add(ingredient)
            db.session.commit()
            flash('تم إضافة المادة العلفية بنجاح!', 'success')
            return redirect(url_for('feed_ingredients'))
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في إضافة المادة العلفية: {str(e)}")
            flash(f'حدث خطأ في إضافة المادة العلفية: {str(e)}', 'error')

    return render_template('feed/add_ingredient.html')

# تعديل المادة العلفية
@app.route('/feed/ingredients/edit/<int:ingredient_id>', methods=['GET', 'POST'])
def edit_feed_ingredient(ingredient_id):
    ingredient = FeedIngredient.query.get_or_404(ingredient_id)

    if request.method == 'POST':
        try:
            ingredient.name = request.form['name']
            ingredient.category = request.form['category']
            ingredient.protein_percentage = float(request.form['protein_percentage']) if request.form['protein_percentage'] else None
            ingredient.energy_value = float(request.form['energy_value']) if request.form['energy_value'] else None
            ingredient.fiber_percentage = float(request.form['fiber_percentage']) if request.form['fiber_percentage'] else None
            ingredient.fat_percentage = float(request.form['fat_percentage']) if request.form['fat_percentage'] else None
            ingredient.moisture_percentage = float(request.form['moisture_percentage']) if request.form['moisture_percentage'] else None
            ingredient.price_per_kg = float(request.form['price_per_kg']) if request.form['price_per_kg'] else None
            ingredient.supplier = request.form['supplier']
            ingredient.notes = request.form['notes']

            db.session.commit()
            flash('تم تحديث المادة العلفية بنجاح!', 'success')
            return redirect(url_for('feed_ingredients'))
        except Exception as e:
            db.session.rollback()
            print(f"خطأ في تحديث المادة العلفية: {str(e)}")
            flash(f'حدث خطأ في تحديث المادة العلفية: {str(e)}', 'error')

    return render_template('feed/edit_ingredient.html', ingredient=ingredient)

# حذف المادة العلفية
@app.route('/feed/ingredients/delete/<int:ingredient_id>', methods=['POST'])
def delete_feed_ingredient(ingredient_id):
    ingredient = FeedIngredient.query.get_or_404(ingredient_id)

    try:
        # التحقق من استخدام المادة في خلطات
        usage_count = FeedMixIngredient.query.filter_by(ingredient_id=ingredient_id).count()
        if usage_count > 0:
            flash(f'لا يمكن حذف هذه المادة لأنها مستخدمة في {usage_count} خلطة/خلطات!', 'error')
        else:
            db.session.delete(ingredient)
            db.session.commit()
            flash('تم حذف المادة العلفية بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"خطأ في حذف المادة العلفية: {str(e)}")
        flash(f'حدث خطأ في حذف المادة العلفية: {str(e)}', 'error')

    return redirect(url_for('feed_ingredients'))

# إدارة الخلطات العلفية
@app.route('/feed/mixes')
def feed_mixes():
    mixes = FeedMix.query.all()
    return render_template('feed/mixes.html', mixes=mixes)

@app.route('/feed/mixes/add', methods=['GET', 'POST'])
def add_feed_mix():
    if request.method == 'POST':
        # إنشاء الخلطة الجديدة
        mix = FeedMix(
            name=request.form['name'],
            description=request.form['description'],
            target_group=request.form['target_group'],
            daily_amount_per_head=float(request.form['daily_amount_per_head']) if request.form['daily_amount_per_head'] else None
        )

        try:
            db.session.add(mix)
            db.session.flush()  # للحصول على ID الخلطة

            # إضافة المكونات
            total_protein = 0
            total_energy = 0
            total_cost = 0

            ingredient_ids = request.form.getlist('ingredient_id')
            percentages = request.form.getlist('percentage')

            for i, ingredient_id in enumerate(ingredient_ids):
                if ingredient_id and percentages[i]:
                    percentage = float(percentages[i])
                    ingredient = FeedIngredient.query.get(ingredient_id)

                    if ingredient:
                        # إضافة المكون للخلطة
                        mix_ingredient = FeedMixIngredient(
                            feed_mix_id=mix.id,
                            ingredient_id=ingredient_id,
                            percentage=percentage,
                            weight_kg=percentage * 10  # لكل 1000 كغ من الخلطة
                        )
                        db.session.add(mix_ingredient)

                        # حساب القيم الغذائية
                        if ingredient.protein_percentage:
                            total_protein += (percentage * ingredient.protein_percentage / 100)
                        if ingredient.energy_value:
                            total_energy += (percentage * ingredient.energy_value / 1000)
                        if ingredient.price_per_kg:
                            total_cost += (percentage * ingredient.price_per_kg / 1000)

            # تحديث قيم الخلطة
            mix.total_protein = total_protein
            mix.total_energy = total_energy
            mix.total_cost_per_kg = total_cost

            db.session.commit()
            flash('تم إنشاء الخلطة العلفية بنجاح!', 'success')
            return redirect(url_for('feed_mixes'))

        except Exception as e:
            db.session.rollback()
            print(f"خطأ في إنشاء الخلطة: {str(e)}")
            flash(f'حدث خطأ في إنشاء الخلطة: {str(e)}', 'error')

    # الحصول على جميع المواد العلفية المتاحة
    ingredients = FeedIngredient.query.all()
    print(f"Debug: عدد المواد العلفية المتاحة: {len(ingredients)}")

    # تحويل المواد العلفية إلى قاموس للـ JSON
    ingredients_data = []
    for ingredient in ingredients:
        ingredient_dict = {
            'id': ingredient.id,
            'name': ingredient.name,
            'category': ingredient.category,
            'protein_percentage': ingredient.protein_percentage or 0,
            'energy_value': ingredient.energy_value or 0,
            'price_per_kg': ingredient.price_per_kg or 0
        }
        ingredients_data.append(ingredient_dict)
        print(f"Debug: مادة علفية: {ingredient.name}")

    print(f"Debug: عدد المواد في ingredients_data: {len(ingredients_data)}")
    return render_template('feed/add_mix.html', ingredients=ingredients, ingredients_data=ingredients_data)

# عرض تفاصيل الخلطة
@app.route('/feed/mixes/<int:mix_id>')
def view_feed_mix(mix_id):
    mix = FeedMix.query.get_or_404(mix_id)
    return render_template('feed/view_mix.html', mix=mix)

# تعديل الخلطة العلفية
@app.route('/feed/mixes/edit/<int:mix_id>', methods=['GET', 'POST'])
def edit_feed_mix(mix_id):
    mix = FeedMix.query.get_or_404(mix_id)

    if request.method == 'POST':
        try:
            # تحديث معلومات الخلطة الأساسية
            mix.name = request.form['name']
            mix.description = request.form['description']
            mix.target_group = request.form['target_group']
            mix.daily_amount_per_head = float(request.form['daily_amount_per_head']) if request.form['daily_amount_per_head'] else None

            # حذف المكونات القديمة
            FeedMixIngredient.query.filter_by(feed_mix_id=mix.id).delete()

            # إضافة المكونات الجديدة
            total_protein = 0
            total_energy = 0
            total_cost = 0

            ingredient_ids = request.form.getlist('ingredient_id')
            percentages = request.form.getlist('percentage')

            for i, ingredient_id in enumerate(ingredient_ids):
                if ingredient_id and percentages[i]:
                    percentage = float(percentages[i])
                    ingredient = FeedIngredient.query.get(ingredient_id)

                    if ingredient:
                        # إضافة المكون للخلطة
                        mix_ingredient = FeedMixIngredient(
                            feed_mix_id=mix.id,
                            ingredient_id=ingredient_id,
                            percentage=percentage,
                            weight_kg=percentage * 10  # لكل 1000 كغ من الخلطة
                        )
                        db.session.add(mix_ingredient)

                        # حساب القيم الغذائية
                        if ingredient.protein_percentage:
                            total_protein += (percentage * ingredient.protein_percentage / 100)
                        if ingredient.energy_value:
                            total_energy += (percentage * ingredient.energy_value / 1000)
                        if ingredient.price_per_kg:
                            total_cost += (percentage * ingredient.price_per_kg / 1000)

            # تحديث قيم الخلطة
            mix.total_protein = total_protein
            mix.total_energy = total_energy
            mix.total_cost_per_kg = total_cost

            db.session.commit()
            flash('تم تحديث الخلطة العلفية بنجاح!', 'success')
            return redirect(url_for('view_feed_mix', mix_id=mix.id))

        except Exception as e:
            db.session.rollback()
            print(f"خطأ في تحديث الخلطة: {str(e)}")
            flash(f'حدث خطأ في تحديث الخلطة: {str(e)}', 'error')

    # الحصول على جميع المواد العلفية المتاحة
    ingredients = FeedIngredient.query.all()

    # تحويل المواد العلفية إلى قاموس للـ JSON
    ingredients_data = []
    for ingredient in ingredients:
        ingredients_data.append({
            'id': ingredient.id,
            'name': ingredient.name,
            'category': ingredient.category,
            'protein_percentage': ingredient.protein_percentage or 0,
            'energy_value': ingredient.energy_value or 0,
            'price_per_kg': ingredient.price_per_kg or 0
        })

    # تحويل مكونات الخلطة الحالية إلى قاموس
    current_mix_data = []
    for mix_ingredient in mix.ingredients:
        current_mix_data.append({
            'ingredient_id': mix_ingredient.ingredient_id,
            'ingredient_name': mix_ingredient.ingredient.name,
            'percentage': mix_ingredient.percentage
        })

    return render_template('feed/edit_mix.html',
                         mix=mix,
                         ingredients=ingredients,
                         ingredients_data=ingredients_data,
                         current_mix_data=current_mix_data)

# حذف الخلطة العلفية
@app.route('/feed/mixes/delete/<int:mix_id>', methods=['POST'])
def delete_feed_mix(mix_id):
    mix = FeedMix.query.get_or_404(mix_id)

    try:
        # حذف جميع مكونات الخلطة أولاً (cascade سيتولى هذا)
        db.session.delete(mix)
        db.session.commit()
        flash('تم حذف الخلطة العلفية بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"خطأ في حذف الخلطة: {str(e)}")
        flash(f'حدث خطأ في حذف الخلطة: {str(e)}', 'error')

    return redirect(url_for('feed_mixes'))

# حاسبة الخلطات العلفية
@app.route('/feed/calculator')
def feed_calculator():
    ingredients = FeedIngredient.query.all()
    print(f"Debug Calculator: عدد المواد العلفية: {len(ingredients)}")

    # تحويل المواد العلفية إلى قاموس للـ JSON
    ingredients_data = []
    for ingredient in ingredients:
        ingredient_dict = {
            'id': ingredient.id,
            'name': ingredient.name,
            'category': ingredient.category,
            'protein_percentage': ingredient.protein_percentage or 0,
            'energy_value': ingredient.energy_value or 0,
            'price_per_kg': ingredient.price_per_kg or 0
        }
        ingredients_data.append(ingredient_dict)
        print(f"Debug Calculator: {ingredient.name}")

    return render_template('feed/calculator.html', ingredients=ingredients, ingredients_data=ingredients_data)

# API لحساب القيم الغذائية
@app.route('/api/feed/calculate', methods=['POST'])
def calculate_feed_nutrition():
    try:
        data = request.get_json()
        ingredients_data = data.get('ingredients', [])

        total_protein = 0
        total_energy = 0
        total_cost = 0
        total_percentage = 0

        for item in ingredients_data:
            ingredient_id = item.get('ingredient_id')
            percentage = float(item.get('percentage', 0))

            ingredient = FeedIngredient.query.get(ingredient_id)
            if ingredient:
                total_percentage += percentage

                if ingredient.protein_percentage:
                    total_protein += (percentage * ingredient.protein_percentage / 100)
                if ingredient.energy_value:
                    total_energy += (percentage * ingredient.energy_value / 1000)
                if ingredient.price_per_kg:
                    total_cost += (percentage * ingredient.price_per_kg / 1000)

        return jsonify({
            'total_protein': round(total_protein, 2),
            'total_energy': round(total_energy, 2),
            'total_cost': round(total_cost, 2),
            'total_percentage': round(total_percentage, 2),
            'is_valid': total_percentage == 100
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 400

# صفحة اختبار الروابط
@app.route('/test')
def test_links():
    # الحصول على أول بقرة للاختبار
    first_cattle = Cattle.query.first()
    cattle_id = first_cattle.id if first_cattle else None
    return render_template('test_links.html', cattle_id=cattle_id)

# اختبار مباشر للروابط
@app.route('/test/health/<int:cattle_id>')
def test_health_link(cattle_id):
    return f"رابط السجل الصحي يعمل للبقرة رقم {cattle_id}! <a href='/health/add/{cattle_id}'>انقر هنا للانتقال</a>"

@app.route('/test/milk/<int:cattle_id>')
def test_milk_link(cattle_id):
    return f"رابط تسجيل الحليب يعمل للبقرة رقم {cattle_id}! <a href='/milk/add/{cattle_id}'>انقر هنا للانتقال</a>"

# البحث المتقدم
@app.route('/search')
def search():
    query = request.args.get('q', '')
    results = []

    if query:
        # البحث في الأبقار
        cattle_results = Cattle.query.filter(
            db.or_(
                Cattle.tag_number.contains(query),
                Cattle.name.contains(query),
                Cattle.breed.contains(query)
            )
        ).all()

        for cattle in cattle_results:
            results.append({
                'type': 'cattle',
                'title': f"البقرة {cattle.tag_number}",
                'description': f"{cattle.name or 'بدون اسم'} - {cattle.breed or 'غير محدد'}",
                'url': url_for('cattle_detail', id=cattle.id)
            })

    return render_template('search/results.html', query=query, results=results)

# إحصائيات متقدمة
@app.route('/api/statistics')
def statistics_api():
    """API للإحصائيات المتقدمة"""

    # إحصائيات الأبقار
    total_cattle = Cattle.query.count()
    cattle_by_status = db.session.query(
        Cattle.status, db.func.count(Cattle.id)
    ).group_by(Cattle.status).all()

    cattle_by_breed = db.session.query(
        Cattle.breed, db.func.count(Cattle.id)
    ).group_by(Cattle.breed).all()

    # إحصائيات الإنتاج
    total_milk_production = db.session.query(db.func.sum(MilkProduction.total_amount)).scalar() or 0
    avg_daily_production = db.session.query(db.func.avg(MilkProduction.total_amount)).scalar() or 0

    # إحصائيات مالية
    total_income = db.session.query(db.func.sum(FinancialRecord.amount)).filter_by(transaction_type='دخل').scalar() or 0
    total_expenses = db.session.query(db.func.sum(FinancialRecord.amount)).filter_by(transaction_type='مصروف').scalar() or 0

    return jsonify({
        'cattle': {
            'total': total_cattle,
            'by_status': dict(cattle_by_status),
            'by_breed': dict(cattle_by_breed)
        },
        'production': {
            'total_milk': total_milk_production,
            'average_daily': round(avg_daily_production, 2)
        },
        'financial': {
            'total_income': total_income,
            'total_expenses': total_expenses,
            'net_profit': total_income - total_expenses
        }
    })

def update_existing_data():
    """تحديث البيانات الموجودة لإضافة قيم افتراضية"""
    try:
        # تحديث الأبقار التي لا تحتوي على جنس
        cattle_without_gender = Cattle.query.filter(
            (Cattle.gender == None) | (Cattle.gender == '')
        ).all()

        for cattle in cattle_without_gender:
            # تعيين جنس افتراضي بناءً على الاسم أو عشوائي
            if cattle.name and ('بقرة' in cattle.name or 'عجلة' in cattle.name):
                cattle.gender = 'أنثى'
            elif cattle.name and ('ثور' in cattle.name or 'عجل' in cattle.name):
                cattle.gender = 'ذكر'
            else:
                # افتراضي: أنثى (لأن معظم الأبقار في المزارع إناث)
                cattle.gender = 'أنثى'

            print(f"تحديث جنس البقرة {cattle.tag_number}: {cattle.gender}")

        db.session.commit()
        print(f"تم تحديث {len(cattle_without_gender)} بقرة")

    except Exception as e:
        print(f"خطأ في تحديث البيانات: {e}")
        db.session.rollback()

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        update_existing_data()
    app.run(debug=True, host='0.0.0.0', port=5000)
