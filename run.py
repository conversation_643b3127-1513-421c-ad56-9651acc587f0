#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الأبقار المتطور
تطوير: نظام ذكي لإدارة المزارع
"""

from app import app, db, Cattle, HealthRecord, MilkProduction, BreedingRecord, FinancialRecord, get_setting, set_setting
from datetime import datetime, date, timedelta
import random

def create_sample_data():
    """إنشاء بيانات تجريبية للنظام"""
    
    # التحقق من وجود بيانات
    if Cattle.query.count() > 0:
        print("البيانات موجودة بالفعل!")
        return
    
    print("إنشاء بيانات تجريبية...")
    
    # إضافة أبقار تجريبية متنوعة
    sample_cattle = [
        {
            'tag_number': 'C001',
            'name': 'فاطمة',
            'breed': 'هولشتاين',
            'gender': 'أنثى',
            'birth_date': date(2020, 3, 15),
            'weight': 450.5,
            'color': 'أسود وأبيض',
            'purchase_price': 1200.00,
            'purchase_date': date(2021, 1, 10),
            'notes': 'بقرة منتجة للحليب بكميات عالية'
        },
        {
            'tag_number': 'C002',
            'name': 'عائشة',
            'breed': 'جيرسي',
            'gender': 'أنثى',
            'birth_date': date(2019, 8, 22),
            'weight': 380.0,
            'color': 'بني',
            'purchase_price': 1000.00,
            'purchase_date': date(2020, 12, 5),
            'notes': 'حليب عالي الجودة'
        },
        {
            'tag_number': 'C003',
            'name': 'خديجة',
            'breed': 'هولشتاين',
            'gender': 'أنثى',
            'birth_date': date(2021, 1, 8),
            'weight': 420.0,
            'color': 'أسود وأبيض',
            'purchase_price': 1100.00,
            'purchase_date': date(2022, 6, 15),
            'notes': 'بقرة شابة منتجة'
        },
        {
            'tag_number': 'C004',
            'name': 'زينب',
            'breed': 'براون سويس',
            'gender': 'أنثى',
            'birth_date': date(2020, 11, 12),
            'weight': 480.0,
            'color': 'بني فاتح',
            'purchase_price': 1300.00,
            'purchase_date': date(2021, 8, 20),
            'notes': 'إنتاج متوسط لكن جودة عالية'
        },
        {
            'tag_number': 'C005',
            'name': 'محمد',
            'breed': 'سيمنتال',
            'gender': 'ذكر',
            'birth_date': date(2018, 5, 10),
            'weight': 650.0,
            'color': 'أحمر وأبيض',
            'purchase_price': 1500.00,
            'purchase_date': date(2019, 3, 20),
            'notes': 'ثور للتكاثر'
        }
    ]
    
    cattle_objects = []
    for cattle_data in sample_cattle:
        cattle = Cattle(**cattle_data)
        db.session.add(cattle)
        cattle_objects.append(cattle)
    
    db.session.commit()
    print(f"تم إضافة {len(sample_cattle)} أبقار")
    
    # إضافة سجلات صحية تجريبية متنوعة
    health_records = [
        {
            'cattle_id': 1,
            'record_type': 'تطعيم',
            'description': 'تطعيم ضد الحمى القلاعية',
            'date': date.today() - timedelta(days=30),
            'veterinarian': 'د. أحمد محمد',
            'cost': 25.00,
            'next_due_date': date.today() + timedelta(days=150)
        },
        {
            'cattle_id': 2,
            'record_type': 'فحص دوري',
            'description': 'فحص عام وتقييم الحالة الصحية',
            'date': date.today() - timedelta(days=15),
            'veterinarian': 'د. سارة أحمد',
            'cost': 15.00
        },
        {
            'cattle_id': 3,
            'record_type': 'علاج',
            'description': 'علاج التهاب الضرع',
            'date': date.today() - timedelta(days=10),
            'veterinarian': 'د. محمد علي',
            'cost': 35.00
        },
        {
            'cattle_id': 4,
            'record_type': 'تطعيم',
            'description': 'تطعيم ضد البروسيلا',
            'date': date.today() - timedelta(days=20),
            'veterinarian': 'د. فاطمة حسن',
            'cost': 30.00,
            'next_due_date': date.today() + timedelta(days=365)
        },
        {
            'cattle_id': 1,
            'record_type': 'فحص دوري',
            'description': 'فحص الحمل',
            'date': date.today() - timedelta(days=5),
            'veterinarian': 'د. أحمد محمد',
            'cost': 20.00
        }
    ]
    
    for record_data in health_records:
        record = HealthRecord(**record_data)
        db.session.add(record)
    
    db.session.commit()
    print(f"تم إضافة {len(health_records)} سجل صحي")
    
    # إضافة سجلات إنتاج حليب تجريبية متنوعة
    milk_records = []

    # معدلات إنتاج مختلفة لكل بقرة
    production_rates = {
        1: (12, 18),  # فاطمة - إنتاج عالي
        2: (8, 14),   # عائشة - إنتاج متوسط
        3: (10, 16),  # خديجة - إنتاج جيد
        4: (6, 12)    # زينب - إنتاج منخفض
    }

    quality_weights = ['ممتاز'] * 4 + ['جيد جداً'] * 3 + ['جيد'] * 2 + ['مقبول'] * 1

    for i in range(30):  # آخر 30 يوم
        record_date = date.today() - timedelta(days=i)

        for cattle_id in [1, 2, 3, 4]:  # للإناث المنتجة فقط
            min_prod, max_prod = production_rates[cattle_id]
            morning = round(random.uniform(min_prod * 0.6, max_prod * 0.6), 1)
            evening = round(random.uniform(min_prod * 0.4, max_prod * 0.4), 1)

            record = MilkProduction(
                cattle_id=cattle_id,
                date=record_date,
                morning_amount=morning,
                evening_amount=evening,
                total_amount=morning + evening,
                quality_grade=random.choice(quality_weights)
            )
            milk_records.append(record)
            db.session.add(record)
    
    db.session.commit()
    print(f"تم إضافة {len(milk_records)} سجل إنتاج حليب")
    
    # إضافة سجلات مالية تجريبية متنوعة
    financial_records = []

    # إيرادات من بيع الحليب (آخر 30 يوم)
    for i in range(30):
        record_date = date.today() - timedelta(days=i)
        daily_income = round(random.uniform(25, 45), 2)

        financial_records.append({
            'transaction_type': 'دخل',
            'category': 'بيع حليب',
            'description': f'بيع حليب يوم {record_date.strftime("%Y/%m/%d")}',
            'amount': daily_income,
            'date': record_date
        })

    # مصروفات متنوعة
    expense_categories = [
        ('علف', 'شراء علف للأبقار', 60, 120),
        ('طبي', 'تكاليف طبية وتطعيمات', 15, 50),
        ('صيانة', 'صيانة المعدات والمرافق', 20, 80),
        ('أخرى', 'مصروفات متنوعة', 10, 30)
    ]

    for i in range(15):  # مصروفات كل يومين تقريباً
        record_date = date.today() - timedelta(days=i*2)
        category, desc, min_amount, max_amount = random.choice(expense_categories)
        amount = round(random.uniform(min_amount, max_amount), 2)

        financial_records.append({
            'transaction_type': 'مصروف',
            'category': category,
            'description': desc,
            'amount': amount,
            'date': record_date,
            'cattle_id': random.choice([1, 2, None]) if category == 'طبي' else None
        })
    
    for record_data in financial_records:
        record = FinancialRecord(**record_data)
        db.session.add(record)
    
    db.session.commit()
    print(f"تم إضافة {len(financial_records)} سجل مالي")
    
    print("تم إنشاء البيانات التجريبية بنجاح!")

def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    print("إعادة تعيين قاعدة البيانات...")
    db.drop_all()
    db.create_all()
    print("تم إعادة تعيين قاعدة البيانات!")

def show_performance_summary():
    """عرض ملخص سريع لتقرير الأداء"""
    print("\n" + "="*60)
    print("📊 ملخص تقرير الأداء")
    print("="*60)

    # حساب إحصائيات سريعة
    total_cattle = Cattle.query.count()
    female_cattle = Cattle.query.filter_by(gender='أنثى').count()

    # إنتاج الحليب آخر 7 أيام
    week_ago = date.today() - timedelta(days=7)
    recent_milk = MilkProduction.query.filter(MilkProduction.date >= week_ago).all()
    total_production = sum(record.total_amount for record in recent_milk)
    avg_daily = total_production / 7 if total_production > 0 else 0

    # الإيرادات والمصروفات آخر 7 أيام
    recent_income = FinancialRecord.query.filter(
        FinancialRecord.transaction_type == 'دخل',
        FinancialRecord.date >= week_ago
    ).all()

    recent_expenses = FinancialRecord.query.filter(
        FinancialRecord.transaction_type == 'مصروف',
        FinancialRecord.date >= week_ago
    ).all()

    total_income = sum(record.amount for record in recent_income)
    total_expenses = sum(record.amount for record in recent_expenses)
    net_profit = total_income - total_expenses

    print(f"🐄 إجمالي الأبقار: {total_cattle} ({female_cattle} منتجة)")
    print(f"🥛 إنتاج آخر 7 أيام: {total_production:.1f} لتر")
    print(f"📈 متوسط يومي: {avg_daily:.1f} لتر")
    print(f"💰 إيرادات الأسبوع: {total_income:.2f} د.أ")
    print(f"💸 مصروفات الأسبوع: {total_expenses:.2f} د.أ")
    print(f"💵 صافي الربح: {net_profit:.2f} د.أ")
    print("="*60)

if __name__ == '__main__':
    with app.app_context():
        # إنشاء الجداول
        db.create_all()

        # تحديث سعر الحليب إلى 0.4 دينار
        set_setting('milk_price', '0.40')
        set_setting('milk_price_updated', datetime.now().strftime('%Y/%m/%d %H:%M'))

        # إنشاء بيانات تجريبية إذا لم تكن موجودة
        create_sample_data()

        print("\n" + "="*60)
        print("🐄 نظام إدارة الأبقار المتطور")
        print("="*60)
        print("✅ التحسينات الجديدة:")
        print("   📊 تقرير الأداء الشامل - جاهز!")
        print("   📈 إجماليات الحليب في الجداول")
        print("   💰 حساب القيم والأرباح تلقائياً")
        print("   📋 مؤشرات الكفاءة والربحية")
        print("   🎯 توصيات التحسين الذكية")
        print("   📊 رسوم بيانية تفاعلية")
        print(f"   🥛 سعر الحليب المحدث: {get_setting('milk_price', '0.40')} د.أ/لتر")
        print("-" * 60)

        # عرض ملخص سريع للأداء
        show_performance_summary()

        print("🌐 النظام جاهز للتشغيل!")
        print("🔗 افتح المتصفح على: http://localhost:5000")
        print("-" * 60)
        print("📊 للوصول لتقرير الأداء:")
        print("   • من الصفحة الرئيسية ← تقرير الأداء")
        print("   • أو مباشرة: http://localhost:5000/reports/performance")
        print("   • أو من التقارير المحفوظة")
        print("="*60 + "\n")

    # تشغيل التطبيق
    app.run(debug=True, host='0.0.0.0', port=5000)
